<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-serif font-bold text-gray-900 mb-4">
          Our Products
        </h1>
        <p class="text-lg text-gray-600">
          Discover authentic Tunisian flavors and traditional specialties
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="flex flex-col lg:flex-row gap-4">
          <!-- Search -->
          <div class="flex-1">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search products..."
                class="input-field pl-10"
                @input="handleSearch"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Category Filter -->
          <div class="lg:w-64">
            <select
              v-model="selectedCategory"
              class="input-field"
              @change="handleCategoryFilter"
            >
              <option value="">All Categories</option>
              <option 
                v-for="category in categories" 
                :key="category.id" 
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- Clear Filters -->
          <button
            v-if="searchQuery || selectedCategory"
            @click="clearFilters"
            class="btn-outline whitespace-nowrap"
          >
            Clear Filters
          </button>
        </div>
      </div>

      <!-- Results Info -->
      <div class="flex justify-between items-center mb-6">
        <p class="text-gray-600">
          {{ filteredProducts.length }} product{{ filteredProducts.length !== 1 ? 's' : '' }} found
        </p>
        
        <div class="flex items-center space-x-4">
          <!-- Sort (placeholder for future implementation) -->
          <select class="input-field w-auto">
            <option>Sort by Name</option>
            <option>Sort by Price</option>
            <option>Sort by Newest</option>
          </select>
        </div>
      </div>

      <!-- Products Grid -->
      <div v-if="!loading && filteredProducts.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <ProductCard 
          v-for="product in filteredProducts" 
          :key="product.id"
          :product="product"
        />
      </div>

      <!-- Loading State -->
      <div v-else-if="loading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div v-for="i in 8" :key="i" class="card animate-pulse">
          <div class="h-48 bg-gray-200"></div>
          <div class="p-4">
            <div class="h-6 bg-gray-200 rounded mb-2"></div>
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-6 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
        <p class="text-gray-600 mb-6">
          Try adjusting your search criteria or browse our categories
        </p>
        <button @click="clearFilters" class="btn-primary">
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import ProductCard from '@/components/ProductCard.vue'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()

const searchQuery = ref('')
const selectedCategory = ref('')
const loading = ref(true)

const categories = computed(() => productsStore.categories)
const filteredProducts = computed(() => productsStore.filteredProducts)

const handleSearch = () => {
  productsStore.searchProducts(searchQuery.value)
  updateURL()
}

const handleCategoryFilter = () => {
  productsStore.filterByCategory(selectedCategory.value || null)
  updateURL()
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  productsStore.clearFilters()
  router.push({ name: 'products' })
}

const updateURL = () => {
  const query: Record<string, string> = {}
  
  if (searchQuery.value) {
    query.search = searchQuery.value
  }
  
  if (selectedCategory.value) {
    query.category = selectedCategory.value
  }
  
  router.push({ name: 'products', query })
}

// Initialize from URL parameters
const initializeFromURL = () => {
  if (route.query.search) {
    searchQuery.value = route.query.search as string
  }
  
  if (route.query.category) {
    selectedCategory.value = route.query.category as string
  }
}

onMounted(async () => {
  try {
    await productsStore.initialize()
    initializeFromURL()
    
    // Apply filters if they exist
    if (searchQuery.value) {
      productsStore.searchProducts(searchQuery.value)
    }
    
    if (selectedCategory.value) {
      productsStore.filterByCategory(selectedCategory.value)
    }
  } catch (error) {
    console.error('Error loading products:', error)
  } finally {
    loading.value = false
  }
})

// Watch for route changes
watch(() => route.query, () => {
  initializeFromURL()
}, { deep: true })
</script>
