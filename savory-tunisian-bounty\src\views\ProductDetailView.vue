<template>
  <div class="min-h-screen bg-gray-50">
    <div v-if="loading" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="animate-pulse">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="h-96 bg-gray-200 rounded-lg"></div>
          <div class="space-y-4">
            <div class="h-8 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-6 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="product" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumb -->
      <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li>
            <router-link to="/" class="hover:text-primary-600">Home</router-link>
          </li>
          <li>/</li>
          <li>
            <router-link to="/products" class="hover:text-primary-600">Products</router-link>
          </li>
          <li>/</li>
          <li class="text-gray-900">{{ product.name }}</li>
        </ol>
      </nav>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Product Images -->
        <div>
          <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4">
            <img 
              :src="product.image_url || '/placeholder-product.jpg'" 
              :alt="product.name"
              class="w-full h-full object-cover"
              @error="handleImageError"
            />
          </div>
          
          <!-- Additional images placeholder -->
          <div v-if="product.images && product.images.length > 0" class="grid grid-cols-4 gap-2">
            <div 
              v-for="(image, index) in product.images.slice(0, 4)" 
              :key="index"
              class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-75"
            >
              <img 
                :src="image" 
                :alt="`${product.name} ${index + 1}`"
                class="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        <!-- Product Info -->
        <div>
          <h1 class="text-3xl font-serif font-bold text-gray-900 mb-4">
            {{ product.name }}
          </h1>
          
          <div class="flex items-center space-x-4 mb-6">
            <span class="text-3xl font-bold text-primary-600">
              ${{ product.price.toFixed(2) }}
            </span>
            <span 
              v-if="product.stock_quantity <= 5" 
              class="bg-red-100 text-red-800 text-sm px-2 py-1 rounded"
            >
              Only {{ product.stock_quantity }} left!
            </span>
            <span 
              v-else-if="product.stock_quantity > 0" 
              class="bg-green-100 text-green-800 text-sm px-2 py-1 rounded"
            >
              In Stock
            </span>
            <span 
              v-else 
              class="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded"
            >
              Out of Stock
            </span>
          </div>

          <div class="prose prose-gray max-w-none mb-8">
            <p>{{ product.description }}</p>
          </div>

          <!-- Quantity and Add to Cart -->
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <div class="flex items-center space-x-3">
                <button
                  @click="quantity = Math.max(1, quantity - 1)"
                  class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                  </svg>
                </button>
                
                <span class="w-16 text-center font-medium text-lg">{{ quantity }}</span>
                
                <button
                  @click="quantity = Math.min(product.stock_quantity, quantity + 1)"
                  :disabled="quantity >= product.stock_quantity"
                  class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex space-x-4">
              <button
                @click="addToCart"
                :disabled="product.stock_quantity === 0 || addingToCart"
                class="flex-1 btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="addingToCart">Adding...</span>
                <span v-else-if="isInCart">Add More to Cart</span>
                <span v-else>Add to Cart</span>
              </button>
              
              <button class="btn-outline px-6 py-3">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
            </div>

            <div v-if="isInCart" class="bg-green-50 border border-green-200 rounded-lg p-4">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="text-green-800">
                  {{ cartQuantity }} item{{ cartQuantity !== 1 ? 's' : '' }} in cart
                </span>
              </div>
            </div>
          </div>

          <!-- Product Details -->
          <div class="mt-8 border-t border-gray-200 pt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Product Details</h3>
            <dl class="space-y-2">
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">SKU:</dt>
                <dd class="text-sm text-gray-900">{{ product.id.slice(0, 8).toUpperCase() }}</dd>
              </div>
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">Stock:</dt>
                <dd class="text-sm text-gray-900">{{ product.stock_quantity }} units</dd>
              </div>
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">Category:</dt>
                <dd class="text-sm text-gray-900">{{ categoryName }}</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Product not found -->
    <div v-else class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
      <div class="text-6xl mb-4">🔍</div>
      <h1 class="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
      <p class="text-gray-600 mb-8">
        The product you're looking for doesn't exist or has been removed.
      </p>
      <router-link to="/products" class="btn-primary">
        Browse Products
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useCartStore } from '@/stores/cart'
import type { Database } from '@/lib/supabase'

type Product = Database['public']['Tables']['products']['Row']

interface Props {
  id: string
}

const props = defineProps<Props>()
const route = useRoute()
const productsStore = useProductsStore()
const cartStore = useCartStore()

const product = ref<Product | null>(null)
const loading = ref(true)
const quantity = ref(1)
const addingToCart = ref(false)

const isInCart = computed(() => product.value ? cartStore.isInCart(product.value.id) : false)
const cartQuantity = computed(() => product.value ? cartStore.getItemQuantity(product.value.id) : 0)

const categoryName = computed(() => {
  if (!product.value?.category_id) return 'Uncategorized'
  const category = productsStore.categories.find(c => c.id === product.value?.category_id)
  return category?.name || 'Uncategorized'
})

const addToCart = async () => {
  if (!product.value || product.value.stock_quantity === 0) return
  
  try {
    addingToCart.value = true
    cartStore.addItem(product.value, quantity.value)
    
    // Reset quantity after adding
    quantity.value = 1
  } catch (error) {
    console.error('Error adding to cart:', error)
  } finally {
    addingToCart.value = false
  }
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400'
}

onMounted(async () => {
  try {
    const productId = props.id || route.params.id as string
    
    // Ensure categories are loaded for category name display
    if (productsStore.categories.length === 0) {
      await productsStore.fetchCategories()
    }
    
    product.value = await productsStore.getProductById(productId)
  } catch (error) {
    console.error('Error loading product:', error)
  } finally {
    loading.value = false
  }
})
</script>
